#!/usr/bin/env python3
"""
Order Tracking System for Binance Futures Trading Bot
Tracks order status, fills, and triggers notifications
"""

import logging
from datetime import datetime, timezone
from typing import Dict, List

logger = logging.getLogger(__name__)

class OrderTracker:
    def __init__(self, trading_service=None):
        self.trading_service = trading_service

    async def get_orders_from_api(self, symbol: str = None) -> List[Dict]:
        """Get orders directly from Binance API"""
        try:
            if not self.trading_service:
                logger.error("❌ Trading service not available")
                return []

            orders = self.trading_service.get_open_orders(symbol)

            if not orders.get('success'):
                logger.error(f"❌ Failed to get orders: {orders.get('error')}")
                return []

            api_orders = orders.get('orders', [])

            formatted_orders = []
            for order in api_orders:
                formatted_order = {
                    'order_id': order.get('orderId'),
                    'symbol': order.get('symbol'),
                    'side': order.get('side'),
                    'type': order.get('type'),
                    'amount': float(order.get('origQty', 0)),
                    'price': float(order.get('price', 0)),
                    'status': order.get('status', '').lower(),
                    'position_side': order.get('positionSide'),
                    'created_at': datetime.fromtimestamp(order.get('time', 0) / 1000, timezone.utc).isoformat(),
                    'updated_at': datetime.fromtimestamp(order.get('updateTime', 0) / 1000, timezone.utc).isoformat()
                }
                formatted_orders.append(formatted_order)

            return formatted_orders

        except Exception as e:
            logger.error(f"❌ Error getting orders from API: {e}")
            return []

    async def cancel_order(self, symbol: str, order_id: str) -> Dict:
        """Cancel order via API"""
        try:
            if not self.trading_service:
                logger.error("❌ Trading service not available")
                return {'success': False, 'error': 'Trading service not available'}

            result = self.trading_service.cancel_order(symbol, order_id)

            if result.get('success'):
                logger.info(f"✅ Order cancelled: {order_id}")
            else:
                logger.error(f"❌ Failed to cancel order: {result.get('error')}")

            return result

        except Exception as e:
            logger.error(f"❌ Error cancelling order: {e}")
            return {'success': False, 'error': str(e)}

    async def get_order_history(self, symbol: str = None, limit: int = 100) -> List[Dict]:
        """Get order history from API"""
        try:
            if not self.trading_service:
                logger.error("❌ Trading service not available")
                return []

            history = self.trading_service.get_order_history(symbol, limit)

            if not history.get('success'):
                logger.error(f"❌ Failed to get order history: {history.get('error')}")
                return []

            return history.get('orders', [])

        except Exception as e:
            logger.error(f"❌ Error getting order history: {e}")
            return []

# Global order tracker instance
order_tracker = OrderTracker()
