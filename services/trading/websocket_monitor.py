#!/usr/bin/env python3
"""
WebSocket Event Monitor for Binance Futures
Real-time monitoring of order fills, position changes, and balance updates
"""

import asyncio
import json
import logging
import websockets
import hmac
import hashlib
import time
from typing import Dict, List, Optional, Callable
from datetime import datetime, timezone

from services.trading.order_tracker import order_tracker
from services.trading.position_manager import position_manager
from services.trading.notification_service import notification_service
from utils.config import get_binance_api_key, get_binance_api_secret

logger = logging.getLogger(__name__)

class BinanceWebSocketMonitor:
    def __init__(self):
        self.api_key = get_binance_api_key()
        self.api_secret = get_binance_api_secret()
        self.base_url = "wss://fstream.binance.com"
        self.listen_key = None
        self.websocket = None
        self.running = False
        self.reconnect_attempts = 0
        self.max_reconnect_attempts = 5

        # Ping/Pong mechanism
        self.ping_interval = 30  # seconds
        self.last_ping_time = None
        self.ping_task = None
        self.connection_start_time = None

        # Price tracking
        self.tracked_symbols = set()
        self.last_prices = {}
        self.price_callbacks = []

        # Statistics
        self.stats = {
            'messages_received': 0,
            'pings_sent': 0,
            'pongs_received': 0,
            'disconnections': 0,
            'price_updates': 0
        }

        # Hybrid tracker integration
        self.hybrid_tracker = None

        # Event handlers
        self.event_handlers = {
            'executionReport': self._handle_execution_report,
            'outboundAccountPosition': self._handle_account_position,
            'outboundAccountInfo': self._handle_account_info
        }

    def set_hybrid_tracker(self, hybrid_tracker):
        """Set hybrid tracker for backup order monitoring"""
        self.hybrid_tracker = hybrid_tracker
        logger.info("🔄 Hybrid tracker integrated with WebSocket monitor")

    def add_price_tracking(self, symbols: list):
        """Add symbols for price tracking"""
        for symbol in symbols:
            self.tracked_symbols.add(symbol.upper())
        logger.info(f"📊 Added price tracking for: {symbols}")

    def add_price_callback(self, callback):
        """Add callback for price updates"""
        self.price_callbacks.append(callback)

    def get_current_prices(self):
        """Get current tracked prices"""
        return self.last_prices.copy()

    def get_connection_stats(self):
        """Get connection statistics"""
        return {
            'running': self.running,
            'reconnect_attempts': self.reconnect_attempts,
            'connection_start_time': self.connection_start_time,
            'stats': self.stats.copy(),
            'tracked_symbols': list(self.tracked_symbols),
            'current_prices': self.last_prices.copy()
        }

    async def start_monitoring(self) -> bool:
        """Start WebSocket monitoring with ping/pong"""
        try:
            logger.info("🚀 Starting Binance WebSocket monitoring...")

            # Get listen key for user data stream
            listen_key = await self._get_listen_key()
            if not listen_key:
                logger.error("❌ Failed to get listen key")
                return False

            self.listen_key = listen_key
            self.running = True
            self.connection_start_time = time.time()

            # Start ping task
            self.ping_task = asyncio.create_task(self._ping_loop())

            # Start both user data and market data streams
            user_task = asyncio.create_task(self._connect_user_stream())
            market_task = asyncio.create_task(self._connect_market_stream())

            # Wait for both to start
            await asyncio.gather(user_task, market_task, return_exceptions=True)

            return True

        except Exception as e:
            logger.error(f"❌ Error starting WebSocket monitoring: {e}")
            return False

    async def stop_monitoring(self):
        """Stop WebSocket monitoring"""
        try:
            logger.info("🛑 Stopping WebSocket monitoring...")
            self.running = False

            # Cancel ping task
            if self.ping_task:
                self.ping_task.cancel()

            if self.websocket:
                await self.websocket.close()

            # Delete listen key
            if self.listen_key:
                await self._delete_listen_key()

            logger.info("✅ WebSocket monitoring stopped")

        except Exception as e:
            logger.error(f"❌ Error stopping WebSocket monitoring: {e}")

    async def _ping_loop(self):
        """Ping/Pong mechanism to keep connection alive"""
        while self.running:
            try:
                await asyncio.sleep(self.ping_interval)

                if not self.running or not self.websocket:
                    break

                # Send ping
                self.last_ping_time = time.time()
                await self.websocket.ping()
                self.stats['pings_sent'] += 1

                logger.debug(f"📡 Ping sent #{self.stats['pings_sent']}")

            except Exception as e:
                logger.error(f"❌ Ping error: {e}")
                break

    async def _get_listen_key(self) -> Optional[str]:
        """Get listen key for user data stream"""
        try:
            import aiohttp

            url = "https://fapi.binance.com/fapi/v1/listenKey"
            headers = {
                'X-MBX-APIKEY': self.api_key
            }

            async with aiohttp.ClientSession() as session:
                async with session.post(url, headers=headers) as response:
                    if response.status == 200:
                        data = await response.json()
                        listen_key = data.get('listenKey')
                        logger.info("✅ Listen key obtained")
                        return listen_key
                    else:
                        logger.error(f"❌ Failed to get listen key: {response.status}")
                        return None

        except Exception as e:
            logger.error(f"❌ Error getting listen key: {e}")
            return None

    async def _delete_listen_key(self):
        """Delete listen key"""
        try:
            import aiohttp

            url = "https://fapi.binance.com/fapi/v1/listenKey"
            headers = {
                'X-MBX-APIKEY': self.api_key
            }

            async with aiohttp.ClientSession() as session:
                async with session.delete(url, headers=headers) as response:
                    if response.status == 200:
                        logger.info("✅ Listen key deleted")
                    else:
                        logger.warning(f"⚠️ Failed to delete listen key: {response.status}")

        except Exception as e:
            logger.error(f"❌ Error deleting listen key: {e}")

    async def _connect_user_stream(self):
        """Connect to User Data Stream for orders/positions"""
        while self.running:
            try:
                ws_url = f"{self.base_url}/ws/{self.listen_key}"
                logger.info(f"🔌 Connecting to User Data Stream: {ws_url}")

                async with websockets.connect(ws_url, ping_interval=None) as websocket:
                    self.websocket = websocket
                    self.reconnect_attempts = 0
                    logger.info("✅ User Data Stream connected successfully")

                    # Notify hybrid tracker of connection
                    if self.hybrid_tracker:
                        self.hybrid_tracker.set_websocket_status(True)

                    # Listen for messages
                    async for message in websocket:
                        if not self.running:
                            break

                        await self._handle_user_message(message)

            except websockets.exceptions.ConnectionClosed:
                if self.running:
                    logger.warning("⚠️ User Data Stream connection closed, attempting to reconnect...")
                    # Notify hybrid tracker of disconnection
                    if self.hybrid_tracker:
                        self.hybrid_tracker.set_websocket_status(False)
                    await self._handle_reconnect()
                else:
                    logger.info("✅ User Data Stream connection closed gracefully")
                    break

            except Exception as e:
                if self.running:
                    logger.error(f"❌ User Data Stream error: {e}")
                    # Notify hybrid tracker of disconnection
                    if self.hybrid_tracker:
                        self.hybrid_tracker.set_websocket_status(False)
                    await self._handle_reconnect()
                else:
                    break

    async def _connect_market_stream(self):
        """Connect to Market Data Stream for prices"""
        if not self.tracked_symbols:
            logger.info("📊 No symbols to track, skipping market data stream")
            return

        while self.running:
            try:
                # Create stream names for tracked symbols
                streams = []
                for symbol in self.tracked_symbols:
                    streams.append(f"{symbol.lower()}@ticker")

                stream_names = "/".join(streams)
                ws_url = f"{self.base_url}/stream?streams={stream_names}"

                logger.info(f"🔌 Connecting to Market Data Stream: {len(self.tracked_symbols)} symbols")

                async with websockets.connect(ws_url, ping_interval=None) as websocket:
                    logger.info("✅ Market Data Stream connected successfully")

                    # Listen for messages
                    async for message in websocket:
                        if not self.running:
                            break

                        await self._handle_market_message(message)

            except websockets.exceptions.ConnectionClosed:
                if self.running:
                    logger.warning("⚠️ Market Data Stream connection closed, attempting to reconnect...")
                    await asyncio.sleep(5)  # Brief pause before reconnect
                else:
                    logger.info("✅ Market Data Stream connection closed gracefully")
                    break

            except Exception as e:
                if self.running:
                    logger.error(f"❌ Market Data Stream error: {e}")
                    await asyncio.sleep(5)  # Brief pause before reconnect
                else:
                    break

    async def _handle_reconnect(self):
        """Handle WebSocket reconnection"""
        self.reconnect_attempts += 1

        if self.reconnect_attempts > self.max_reconnect_attempts:
            logger.error(f"❌ Max reconnection attempts ({self.max_reconnect_attempts}) reached")
            self.running = False
            return

        wait_time = min(2 ** self.reconnect_attempts, 60)  # Exponential backoff, max 60s
        logger.info(f"⏳ Waiting {wait_time}s before reconnection attempt {self.reconnect_attempts}")

        await asyncio.sleep(wait_time)

        # Refresh listen key
        new_listen_key = await self._get_listen_key()
        if new_listen_key:
            self.listen_key = new_listen_key
        else:
            logger.error("❌ Failed to refresh listen key")
            self.running = False

    async def _handle_user_message(self, message: str):
        """Handle User Data Stream messages"""
        try:
            self.stats['messages_received'] += 1
            data = json.loads(message)
            event_type = data.get('e')

            if event_type in self.event_handlers:
                await self.event_handlers[event_type](data)
            else:
                logger.debug(f"📨 Unhandled user event type: {event_type}")

        except json.JSONDecodeError:
            logger.error(f"❌ Failed to parse user message: {message}")
        except Exception as e:
            logger.error(f"❌ Error handling user message: {e}")

    async def _handle_market_message(self, message: str):
        """Handle Market Data Stream messages"""
        try:
            self.stats['messages_received'] += 1
            data = json.loads(message)

            # Market data comes wrapped in stream format
            if 'stream' in data and 'data' in data:
                stream_data = data['data']
                event_type = stream_data.get('e')

                if event_type == '24hrTicker':
                    await self._handle_price_update(stream_data)
                else:
                    logger.debug(f"📨 Unhandled market event type: {event_type}")

        except json.JSONDecodeError:
            logger.error(f"❌ Failed to parse market message: {message}")
        except Exception as e:
            logger.error(f"❌ Error handling market message: {e}")

    async def _handle_price_update(self, data: Dict):
        """Handle price ticker updates"""
        try:
            symbol = data.get('s')
            price = float(data.get('c', 0))  # Close price

            if symbol and price > 0 and symbol in self.tracked_symbols:
                old_price = self.last_prices.get(symbol, 0)
                self.last_prices[symbol] = price
                self.stats['price_updates'] += 1

                # Call price callbacks
                for callback in self.price_callbacks:
                    try:
                        callback(symbol, price)
                    except Exception as e:
                        logger.error(f"❌ Price callback error: {e}")

                # Log significant price changes
                if old_price > 0:
                    change_pct = ((price - old_price) / old_price) * 100
                    if abs(change_pct) > 1.0:  # Log if >1% change
                        logger.info(f"📈 {symbol}: ${price:,.2f} ({change_pct:+.2f}%)")

        except Exception as e:
            logger.error(f"❌ Error handling price update: {e}")

    async def _handle_execution_report(self, data: Dict):
        """Handle order execution report (fills, cancellations, etc.)"""
        try:
            order_id = str(data.get('i'))  # Order ID
            symbol = data.get('s')  # Symbol
            side = data.get('S', '').lower()  # Side (BUY/SELL)
            order_status = data.get('X')  # Order status
            execution_type = data.get('x')  # Execution type

            # Quantities and prices
            order_quantity = float(data.get('q', 0))  # Original quantity
            filled_quantity = float(data.get('z', 0))  # Filled quantity
            last_filled_quantity = float(data.get('l', 0))  # Last filled quantity
            last_filled_price = float(data.get('L', 0))  # Last filled price

            logger.info(f"📊 Execution Report: {symbol} {side} {order_status} - {order_id}")

            # Handle different execution types
            if execution_type == 'TRADE' and last_filled_quantity > 0:
                # Order fill detected
                await self._handle_order_fill(
                    order_id, symbol, side, last_filled_quantity,
                    last_filled_price, filled_quantity, order_quantity
                )

            elif order_status in ['CANCELED', 'REJECTED', 'EXPIRED']:
                # Order cancelled/rejected
                await self._handle_order_cancellation(order_id, order_status)

            elif order_status == 'FILLED':
                # Order completely filled
                await self._handle_order_complete(order_id)

        except Exception as e:
            logger.error(f"❌ Error handling execution report: {e}")

    async def _handle_order_fill(self, order_id: str, symbol: str, side: str,
                                fill_quantity: float, fill_price: float,
                                total_filled: float, order_quantity: float):
        """Handle order fill event"""
        try:
            logger.info(f"🎯 Order Fill: {order_id} - {fill_quantity} @ {fill_price}")

            # Log order fill event
            fill_data = {
                'price': fill_price,
                'amount': fill_quantity,
                'total_filled': total_filled
            }

            # Determine if order is completely filled
            is_complete = abs(total_filled - order_quantity) < 0.000001
            status = 'filled' if is_complete else 'partially_filled'

            logger.info(f"📊 Order {order_id} status: {status}")

            # Send fill notification (simplified without database lookup)
            await self._send_simple_fill_notification(order_id, fill_data, is_complete)

        except Exception as e:
            logger.error(f"❌ Error handling order fill: {e}")

    async def _send_simple_fill_notification(self, order_id: str, fill_data: Dict, is_complete: bool):
        """Send simplified fill notification"""
        try:
            logger.info(f"📢 Order Fill Notification: {order_id} - ${fill_data['price']:,.2f} x {fill_data['amount']:.6f}")
            # In a real implementation, this would send Discord notifications
        except Exception as e:
            logger.error(f"❌ Error sending fill notification: {e}")

    async def _handle_order_cancellation(self, order_id: str, status: str):
        """Handle order cancellation"""
        try:
            logger.info(f"❌ Order {status}: {order_id}")

            # Update order status
            order_tracker.update_order_status(order_id, status.lower())

            # Get order details and send notification
            orders = order_tracker.db.get_orders()
            order = next((o for o in orders if o['order_id'] == order_id), None)

            if order:
                await notification_service.send_order_notification(
                    order['user_id'], 'order_cancelled', order
                )

        except Exception as e:
            logger.error(f"❌ Error handling order cancellation: {e}")

    async def _handle_order_complete(self, order_id: str):
        """Handle order completion"""
        try:
            logger.info(f"✅ Order Complete: {order_id}")
            # Additional logic for order completion if needed

        except Exception as e:
            logger.error(f"❌ Error handling order completion: {e}")

    async def _handle_account_position(self, data: Dict):
        """Handle account position update"""
        try:
            symbol = data.get('s')
            position_amount = float(data.get('pa', 0))
            position_side = data.get('ps')

            logger.info(f"📊 Position Update: {symbol} {position_side} {position_amount}")

            # Update position prices with current market data
            # This would typically trigger a price update for all relevant positions

        except Exception as e:
            logger.error(f"❌ Error handling account position: {e}")

    async def _handle_account_info(self, data: Dict):
        """Handle account info update (balance changes)"""
        try:
            balances = data.get('B', [])
            for balance in balances:
                asset = balance.get('a')
                free = float(balance.get('f', 0))
                locked = float(balance.get('l', 0))

                if asset == 'USDT':
                    logger.info(f"💰 USDT Balance: Free={free:.2f}, Locked={locked:.2f}")

        except Exception as e:
            logger.error(f"❌ Error handling account info: {e}")

    async def _send_fill_notification(self, order: Dict, fill_data: Dict, is_complete: bool):
        """Send notification for order fill"""
        try:
            # Prepare notification data
            notification_data = {
                'order_id': order['order_id'],
                'symbol': order['symbol'],
                'side': order['side'],
                'filled_amount': fill_data['amount'],
                'filled_price': fill_data['price'],
                'is_complete': is_complete
            }

            # Calculate P&L if possible
            if order['type'] == 'limit':
                pnl = (fill_data['price'] - order['price']) * fill_data['amount']
                if order['side'] == 'sell':
                    pnl = -pnl
                notification_data['pnl'] = pnl

            # Send notification
            await notification_service.send_order_notification(
                order['user_id'], 'order_filled', notification_data
            )

        except Exception as e:
            logger.error(f"❌ Error sending fill notification: {e}")

# Global WebSocket monitor instance
websocket_monitor = BinanceWebSocketMonitor()
