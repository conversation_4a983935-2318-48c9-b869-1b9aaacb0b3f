#!/usr/bin/env python3
"""
Position Management System for Binance Futures Trading Bot
Handles position calculation, P&L tracking, and position lifecycle management
"""

import logging
from datetime import datetime, timezone
from typing import Dict, List

logger = logging.getLogger(__name__)

class PositionManager:
    def __init__(self, trading_service=None):
        self.trading_service = trading_service

    async def get_positions_from_api(self, symbol: str = None) -> List[Dict]:
        """Get positions directly from Binance API"""
        try:
            if not self.trading_service:
                logger.error("❌ Trading service not available")
                return []

            positions = self.trading_service.get_positions()

            if not positions.get('success'):
                logger.error(f"❌ Failed to get positions: {positions.get('error')}")
                return []

            api_positions = positions.get('positions', [])

            if symbol:
                api_positions = [p for p in api_positions if p.get('symbol', '').upper() == symbol.upper()]

            formatted_positions = []
            for pos in api_positions:
                if float(pos.get('positionAmt', 0)) != 0:
                    formatted_pos = {
                        'symbol': pos.get('symbol'),
                        'position_side': 'LONG' if float(pos.get('positionAmt', 0)) > 0 else 'SHORT',
                        'amount': abs(float(pos.get('positionAmt', 0))),
                        'entry_price': float(pos.get('entryPrice', 0)),
                        'current_price': float(pos.get('markPrice', 0)),
                        'unrealized_pnl': float(pos.get('unRealizedPnl', 0)),
                        'margin_used': float(pos.get('initialMargin', 0)),
                        'percentage': float(pos.get('percentage', 0)),
                        'updated_at': datetime.now(timezone.utc).isoformat()
                    }
                    formatted_positions.append(formatted_pos)

            return formatted_positions

        except Exception as e:
            logger.error(f"❌ Error getting positions from API: {e}")
            return []

    async def get_account_info(self) -> Dict:
        """Get account information from API"""
        try:
            if not self.trading_service:
                logger.error("❌ Trading service not available")
                return {}

            account_info = self.trading_service.get_account_info()

            if not account_info.get('success'):
                logger.error(f"❌ Failed to get account info: {account_info.get('error')}")
                return {}

            return account_info.get('account', {})

        except Exception as e:
            logger.error(f"❌ Error getting account info: {e}")
            return {}

    async def get_pnl_summary(self, symbol: str = None, period: str = "today") -> Dict:
        """Get P&L summary from positions"""
        try:
            positions = await self.get_positions_from_api(symbol)

            total_unrealized = sum(pos.get('unrealized_pnl', 0) for pos in positions)
            total_margin = sum(pos.get('margin_used', 0) for pos in positions)

            return {
                'total_unrealized_pnl': total_unrealized,
                'total_margin_used': total_margin,
                'position_count': len(positions),
                'positions': positions
            }

        except Exception as e:
            logger.error(f"❌ Error getting P&L summary: {e}")
            return {}

    async def close_position(self, symbol: str, side: str = None, percentage: float = 100.0) -> Dict:
        """Close position via API"""
        try:
            if not self.trading_service:
                logger.error("❌ Trading service not available")
                return {'success': False, 'error': 'Trading service not available'}

            result = self.trading_service.close_position(symbol, side, percentage)

            if result.get('success'):
                logger.info(f"✅ Position closed: {symbol} {side} {percentage}%")
            else:
                logger.error(f"❌ Failed to close position: {result.get('error')}")

            return result

        except Exception as e:
            logger.error(f"❌ Error closing position: {e}")
            return {'success': False, 'error': str(e)}

    async def set_take_profit(self, symbol: str, side: str, price: float = None, percentage: float = None) -> Dict:
        """Set take profit for position"""
        try:
            if not self.trading_service:
                logger.error("❌ Trading service not available")
                return {'success': False, 'error': 'Trading service not available'}

            result = self.trading_service.set_take_profit(symbol, side, price, percentage)

            if result.get('success'):
                logger.info(f"✅ Take profit set: {symbol} {side}")
            else:
                logger.error(f"❌ Failed to set take profit: {result.get('error')}")

            return result

        except Exception as e:
            logger.error(f"❌ Error setting take profit: {e}")
            return {'success': False, 'error': str(e)}

    async def set_stop_loss(self, symbol: str, side: str, price: float = None, percentage: float = None) -> Dict:
        """Set stop loss for position"""
        try:
            if not self.trading_service:
                logger.error("❌ Trading service not available")
                return {'success': False, 'error': 'Trading service not available'}

            result = self.trading_service.set_stop_loss(symbol, side, price, percentage)

            if result.get('success'):
                logger.info(f"✅ Stop loss set: {symbol} {side}")
            else:
                logger.error(f"❌ Failed to set stop loss: {result.get('error')}")

            return result

        except Exception as e:
            logger.error(f"❌ Error setting stop loss: {e}")
            return {'success': False, 'error': str(e)}

# Global position manager instance
position_manager = PositionManager()
