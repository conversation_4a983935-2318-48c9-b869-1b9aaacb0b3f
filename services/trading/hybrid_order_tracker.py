#!/usr/bin/env python3
"""
Hybrid Order Tracking System - Combines WebSocket real-time + API polling backup
Solves Single Point of Failure issue when WebSocket disconnects
"""

import asyncio
import logging
from datetime import datetime, timezone, timedelta
from typing import Dict, List, Optional, Any
from services.data.database import trading_db
from services.trading.trading_service import BinanceFuturesTrading
from services.trading.order_tracker import OrderTracker
from services.trading.position_manager import position_manager
from services.trading.notification_service import notification_service

logger = logging.getLogger(__name__)

class HybridOrderTracker:
    """
    Hybrid order tracking system that combines:
    1. WebSocket real-time monitoring (primary)
    2. API polling backup (fallback)
    3. Order reconciliation system
    """

    def __init__(self, trading_service: BinanceFuturesTrading = None):
        self.trading_service = trading_service
        self.db = trading_db
        self.order_tracker = OrderTracker(trading_service)
        
        # Backup tracking configuration
        self.backup_polling_interval = 30  # seconds
        self.backup_polling_enabled = False
        self.backup_task = None
        
        # Order reconciliation
        self.last_reconciliation = None
        self.reconciliation_interval = 300  # 5 minutes
        
        # Tracking state
        self.websocket_connected = False
        self.pending_orders = set()  # Orders waiting for fills
        
        logger.info("🔄 Hybrid Order Tracker initialized")

    async def start_backup_tracking(self):
        """Start backup API polling system"""
        if self.backup_task and not self.backup_task.done():
            logger.warning("Backup tracking already running")
            return
            
        self.backup_polling_enabled = True
        self.backup_task = asyncio.create_task(self._backup_polling_loop())
        logger.info("🔄 Backup order tracking started")

    async def stop_backup_tracking(self):
        """Stop backup API polling system"""
        self.backup_polling_enabled = False
        if self.backup_task:
            self.backup_task.cancel()
            try:
                await self.backup_task
            except asyncio.CancelledError:
                pass
        logger.info("⏹️ Backup order tracking stopped")

    def set_websocket_status(self, connected: bool):
        """Update WebSocket connection status"""
        was_connected = self.websocket_connected
        self.websocket_connected = connected
        
        if was_connected and not connected:
            logger.warning("⚠️ WebSocket disconnected - Activating backup tracking")
            asyncio.create_task(self.start_backup_tracking())
        elif not was_connected and connected:
            logger.info("✅ WebSocket reconnected - Backup tracking will continue as safety net")

    async def track_order(self, order_result: Dict, user_id: str, metadata: Dict = None) -> bool:
        """Track new order using both systems"""
        try:
            # Use existing order tracker
            success = self.order_tracker.track_order(order_result, user_id, metadata)
            
            if success:
                order_id = str(order_result.get('order_id', ''))
                self.pending_orders.add(order_id)
                logger.info(f"📊 Hybrid tracking started for order: {order_id}")
                
                # Start backup tracking if WebSocket is down
                if not self.websocket_connected:
                    await self.start_backup_tracking()
            
            return success
            
        except Exception as e:
            logger.error(f"❌ Failed to start hybrid tracking: {e}")
            return False

    async def _backup_polling_loop(self):
        """Main backup polling loop"""
        logger.info("🔄 Starting backup polling loop")
        
        while self.backup_polling_enabled:
            try:
                await self._check_pending_orders()
                await asyncio.sleep(self.backup_polling_interval)
                
                # Periodic reconciliation
                if self._should_reconcile():
                    await self._reconcile_orders()
                    
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"❌ Error in backup polling loop: {e}")
                await asyncio.sleep(5)  # Brief pause before retry

    async def _check_pending_orders(self):
        """Check status of pending orders via API"""
        if not self.trading_service or not self.pending_orders:
            return
            
        orders_to_check = list(self.pending_orders)
        logger.debug(f"🔍 Checking {len(orders_to_check)} pending orders via API")
        
        for order_id in orders_to_check:
            try:
                await self._check_single_order(order_id)
            except Exception as e:
                logger.error(f"❌ Error checking order {order_id}: {e}")

    async def _check_single_order(self, order_id: str):
        """Check single order status via API"""
        try:
            # Get order from database
            orders = self.db.get_orders()
            order = next((o for o in orders if o['order_id'] == order_id), None)
            
            if not order:
                logger.warning(f"⚠️ Order not found in database: {order_id}")
                self.pending_orders.discard(order_id)
                return
                
            # Skip if already filled
            if order['status'] in ['filled', 'cancelled', 'rejected']:
                self.pending_orders.discard(order_id)
                return
                
            # Check status via API
            api_status = await self._get_order_status_from_api(order_id, order['symbol'])
            
            if api_status:
                await self._process_api_order_update(order, api_status)
                
        except Exception as e:
            logger.error(f"❌ Error checking single order {order_id}: {e}")

    async def _get_order_status_from_api(self, order_id: str, symbol: str) -> Optional[Dict]:
        """Get order status from Binance API"""
        try:
            if not self.trading_service:
                return None
                
            # Use trading service to get order status
            from services.core.symbol_service import format_symbol_for_exchange
            formatted_symbol = format_symbol_for_exchange(symbol)
            
            loop = asyncio.get_event_loop()
            order_info = await loop.run_in_executor(
                None, 
                self.trading_service.exchange.fetch_order,
                order_id,
                formatted_symbol
            )
            
            return order_info
            
        except Exception as e:
            logger.error(f"❌ Failed to get order status from API: {e}")
            return None

    async def _process_api_order_update(self, order: Dict, api_status: Dict):
        """Process order update from API"""
        try:
            order_id = order['order_id']
            api_order_status = api_status.get('status', '').lower()
            current_status = order['status']
            
            # Check if status changed
            if api_order_status != current_status:
                logger.info(f"📊 API detected status change: {order_id} {current_status} -> {api_order_status}")
                
                if api_order_status == 'closed':  # Binance uses 'closed' for filled
                    await self._handle_api_fill(order, api_status)
                elif api_order_status in ['canceled', 'rejected', 'expired']:
                    await self._handle_api_cancellation(order, api_order_status)
                    
        except Exception as e:
            logger.error(f"❌ Error processing API order update: {e}")

    async def _handle_api_fill(self, order: Dict, api_status: Dict):
        """Handle order fill detected via API"""
        try:
            order_id = order['order_id']
            filled_amount = float(api_status.get('filled', 0))
            average_price = float(api_status.get('average', 0))
            
            if filled_amount > 0 and average_price > 0:
                logger.info(f"🎯 API detected fill: {order_id} - {filled_amount} @ {average_price}")
                
                # Create fill data
                fill_data = {
                    'price': average_price,
                    'amount': filled_amount,
                    'total_filled': filled_amount
                }
                
                # Update order status
                self.order_tracker.update_order_status(order_id, 'filled', fill_data)
                
                # Update position
                position_manager.update_position_from_fill(order, fill_data)
                
                # Send notification
                await notification_service.send_order_notification(
                    order['user_id'], 'order_filled', {
                        'order_id': order_id,
                        'symbol': order['symbol'],
                        'side': order['side'],
                        'filled_amount': filled_amount,
                        'filled_price': average_price,
                        'is_complete': True,
                        'source': 'API_BACKUP'
                    }
                )
                
                # Remove from pending
                self.pending_orders.discard(order_id)
                
        except Exception as e:
            logger.error(f"❌ Error handling API fill: {e}")

    async def _handle_api_cancellation(self, order: Dict, status: str):
        """Handle order cancellation detected via API"""
        try:
            order_id = order['order_id']
            logger.info(f"❌ API detected cancellation: {order_id} - {status}")
            
            # Update order status
            self.order_tracker.update_order_status(order_id, status)
            
            # Send notification
            await notification_service.send_order_notification(
                order['user_id'], 'order_cancelled', order
            )
            
            # Remove from pending
            self.pending_orders.discard(order_id)
            
        except Exception as e:
            logger.error(f"❌ Error handling API cancellation: {e}")

    def _should_reconcile(self) -> bool:
        """Check if reconciliation should run"""
        if not self.last_reconciliation:
            return True
            
        time_since_last = datetime.now(timezone.utc) - self.last_reconciliation
        return time_since_last.total_seconds() >= self.reconciliation_interval

    async def _reconcile_orders(self):
        """Reconcile orders between database and exchange"""
        try:
            logger.info("🔄 Starting order reconciliation")
            self.last_reconciliation = datetime.now(timezone.utc)
            
            # Get all open orders from database
            orders = self.db.get_orders()
            open_orders = [o for o in orders if o['status'] in ['open', 'partially_filled']]
            
            reconciled_count = 0
            for order in open_orders:
                try:
                    api_status = await self._get_order_status_from_api(
                        order['order_id'], order['symbol']
                    )
                    if api_status:
                        await self._process_api_order_update(order, api_status)
                        reconciled_count += 1
                except Exception as e:
                    logger.error(f"❌ Error reconciling order {order['order_id']}: {e}")
            
            logger.info(f"✅ Order reconciliation completed: {reconciled_count}/{len(open_orders)} orders checked")
            
        except Exception as e:
            logger.error(f"❌ Error in order reconciliation: {e}")

    def get_status(self) -> Dict[str, Any]:
        """Get hybrid tracker status"""
        return {
            'websocket_connected': self.websocket_connected,
            'backup_polling_enabled': self.backup_polling_enabled,
            'pending_orders_count': len(self.pending_orders),
            'last_reconciliation': self.last_reconciliation.isoformat() if self.last_reconciliation else None,
            'backup_polling_interval': self.backup_polling_interval
        }

# Global hybrid tracker instance
hybrid_order_tracker = HybridOrderTracker()
