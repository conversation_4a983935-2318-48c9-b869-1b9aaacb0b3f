#!/usr/bin/env python3
"""
Position Management Commands for Trading Bot
Handles /tp, /sl, /closeall, /closeside, /closepos commands
"""

import logging
import discord
from discord.ext import commands
from discord import app_commands
from typing import Optional

from services.trading.position_manager import position_manager
from services.trading.trading_service import BinanceFuturesTrading

logger = logging.getLogger(__name__)

class PositionCommands(commands.Cog):
    def __init__(self, bot):
        self.bot = bot
        self.trading_service = None
        self.trade_channel_name = "trade"

    async def cog_load(self):
        """Initialize trading service when cog loads"""
        try:
            self.trading_service = BinanceFuturesTrading()
            position_manager.trading_service = self.trading_service
            logger.info("✅ Position management service initialized")
        except Exception as e:
            logger.error(f"❌ Failed to initialize position service: {e}")
            self.trading_service = None

    def _check_trade_channel(self, interaction: discord.Interaction) -> bool:
        """Check if command is used in correct channel"""
        return interaction.channel.name == self.trade_channel_name

    def _normalize_symbol(self, symbol: str) -> str:
        """Normalize symbol format"""
        symbol = symbol.upper().replace('USDT', '').replace('PERP', '')
        if not symbol.endswith('USDT'):
            symbol += 'USDT'
        return symbol

    @app_commands.command(name="tp", description="Set take profit - /tp symbol side price [percentage]")
    @app_commands.describe(
        symbol="Symbol (vd: btc, eth)",
        side="Position side (LONG/SHORT)",
        price="Take profit price (optional if using percentage)",
        percentage="Percentage of position to close (25/50/75/100)"
    )
    async def set_take_profit(self, interaction: discord.Interaction, 
                             symbol: str, side: str, 
                             price: Optional[float] = None,
                             percentage: Optional[float] = 100.0):
        """Set take profit for position"""
        
        if not self._check_trade_channel(interaction):
            await interaction.response.send_message(
                f"❌ This command only works in #{self.trade_channel_name}",
                ephemeral=True
            )
            return

        if not self.trading_service:
            await interaction.response.send_message("❌ Trading service not available", ephemeral=True)
            return

        await interaction.response.defer()

        try:
            symbol = self._normalize_symbol(symbol)
            side = side.upper()

            if side not in ['LONG', 'SHORT']:
                await interaction.followup.send("❌ Side must be LONG or SHORT")
                return

            if percentage not in [25.0, 50.0, 75.0, 100.0]:
                await interaction.followup.send("❌ Percentage must be 25, 50, 75, or 100")
                return

            result = await position_manager.set_take_profit(symbol, side, price, percentage)

            if result.get('success'):
                embed = discord.Embed(
                    title="✅ Take Profit Set",
                    color=0x00ff88,
                    timestamp=discord.utils.utcnow()
                )
                embed.add_field(name="Symbol", value=symbol, inline=True)
                embed.add_field(name="Side", value=side, inline=True)
                embed.add_field(name="Percentage", value=f"{percentage}%", inline=True)
                
                if price:
                    embed.add_field(name="Price", value=f"${price:,.2f}", inline=True)
                
                embed.add_field(name="Order ID", value=result.get('order_id', 'N/A'), inline=False)
            else:
                embed = discord.Embed(
                    title="❌ Take Profit Failed",
                    description=f"Error: {result.get('error')}",
                    color=0xff4444,
                    timestamp=discord.utils.utcnow()
                )

            embed.set_footer(text="Binance Futures Trading")
            await interaction.followup.send(embed=embed)

        except Exception as e:
            logger.error(f"Error in take profit command: {e}")
            await interaction.followup.send(f"❌ Error: {str(e)}")

    @app_commands.command(name="sl", description="Set stop loss - /sl symbol side price [percentage]")
    @app_commands.describe(
        symbol="Symbol (vd: btc, eth)",
        side="Position side (LONG/SHORT)",
        price="Stop loss price (optional if using percentage)",
        percentage="Percentage of position to close (25/50/75/100)"
    )
    async def set_stop_loss(self, interaction: discord.Interaction, 
                           symbol: str, side: str, 
                           price: Optional[float] = None,
                           percentage: Optional[float] = 100.0):
        """Set stop loss for position"""
        
        if not self._check_trade_channel(interaction):
            await interaction.response.send_message(
                f"❌ This command only works in #{self.trade_channel_name}",
                ephemeral=True
            )
            return

        if not self.trading_service:
            await interaction.response.send_message("❌ Trading service not available", ephemeral=True)
            return

        await interaction.response.defer()

        try:
            symbol = self._normalize_symbol(symbol)
            side = side.upper()

            if side not in ['LONG', 'SHORT']:
                await interaction.followup.send("❌ Side must be LONG or SHORT")
                return

            if percentage not in [25.0, 50.0, 75.0, 100.0]:
                await interaction.followup.send("❌ Percentage must be 25, 50, 75, or 100")
                return

            result = await position_manager.set_stop_loss(symbol, side, price, percentage)

            if result.get('success'):
                embed = discord.Embed(
                    title="✅ Stop Loss Set",
                    color=0x00ff88,
                    timestamp=discord.utils.utcnow()
                )
                embed.add_field(name="Symbol", value=symbol, inline=True)
                embed.add_field(name="Side", value=side, inline=True)
                embed.add_field(name="Percentage", value=f"{percentage}%", inline=True)
                
                if price:
                    embed.add_field(name="Price", value=f"${price:,.2f}", inline=True)
                
                embed.add_field(name="Order ID", value=result.get('order_id', 'N/A'), inline=False)
            else:
                embed = discord.Embed(
                    title="❌ Stop Loss Failed",
                    description=f"Error: {result.get('error')}",
                    color=0xff4444,
                    timestamp=discord.utils.utcnow()
                )

            embed.set_footer(text="Binance Futures Trading")
            await interaction.followup.send(embed=embed)

        except Exception as e:
            logger.error(f"Error in stop loss command: {e}")
            await interaction.followup.send(f"❌ Error: {str(e)}")

    @app_commands.command(name="closeall", description="Close all positions - /closeall confirm:yes")
    @app_commands.describe(confirm="Type 'yes' to confirm closing all positions")
    async def close_all_positions(self, interaction: discord.Interaction, confirm: str):
        """Close all open positions"""
        
        if not self._check_trade_channel(interaction):
            await interaction.response.send_message(
                f"❌ This command only works in #{self.trade_channel_name}",
                ephemeral=True
            )
            return

        if confirm != "yes":
            await interaction.response.send_message(
                "⚠️ This will close ALL positions! Add `confirm:yes` to proceed",
                ephemeral=True
            )
            return

        if not self.trading_service:
            await interaction.response.send_message("❌ Trading service not available", ephemeral=True)
            return

        await interaction.response.defer()

        try:
            # Get all open positions
            positions = await position_manager.get_positions_from_api()
            
            if not positions:
                await interaction.followup.send("❌ No open positions found")
                return

            # Close each position
            closed_count = 0
            total_pnl = 0
            
            for position in positions:
                symbol = position.get('symbol')
                side = position.get('position_side')
                
                result = await position_manager.close_position(symbol, side, 100.0)
                if result.get('success'):
                    closed_count += 1
                    total_pnl += position.get('unrealized_pnl', 0)

            embed = discord.Embed(
                title="🔒 All Positions Closed",
                color=0x00ff88 if total_pnl >= 0 else 0xff4444,
                timestamp=discord.utils.utcnow()
            )
            
            pnl_color = "🟢" if total_pnl >= 0 else "🔴"
            embed.add_field(name="Positions Closed", value=f"{closed_count}/{len(positions)}", inline=True)
            embed.add_field(name="Total P&L", value=f"{pnl_color} ${total_pnl:,.2f}", inline=True)

            embed.set_footer(text="Binance Futures Trading")
            await interaction.followup.send(embed=embed)

        except Exception as e:
            logger.error(f"Error in close all command: {e}")
            await interaction.followup.send(f"❌ Error: {str(e)}")

    @app_commands.command(name="closeside", description="Close all LONG or SHORT positions - /closeside side confirm:yes")
    @app_commands.describe(
        side="Position side to close (LONG/SHORT)",
        confirm="Type 'yes' to confirm"
    )
    async def close_side_positions(self, interaction: discord.Interaction, side: str, confirm: str):
        """Close all positions of specified side"""

        if not self._check_trade_channel(interaction):
            await interaction.response.send_message(
                f"❌ This command only works in #{self.trade_channel_name}",
                ephemeral=True
            )
            return

        if confirm != "yes":
            await interaction.response.send_message(
                f"⚠️ This will close ALL {side.upper()} positions! Add `confirm:yes` to proceed",
                ephemeral=True
            )
            return

        if not self.trading_service:
            await interaction.response.send_message("❌ Trading service not available", ephemeral=True)
            return

        await interaction.response.defer()

        try:
            side = side.upper()
            if side not in ['LONG', 'SHORT']:
                await interaction.followup.send("❌ Side must be LONG or SHORT")
                return

            # Get all open positions
            positions = await position_manager.get_positions_from_api()
            side_positions = [p for p in positions if p.get('position_side') == side]

            if not side_positions:
                await interaction.followup.send(f"❌ No open {side} positions found")
                return

            # Close each position
            closed_count = 0
            total_pnl = 0

            for position in side_positions:
                symbol = position.get('symbol')

                result = await position_manager.close_position(symbol, side, 100.0)
                if result.get('success'):
                    closed_count += 1
                    total_pnl += position.get('unrealized_pnl', 0)

            embed = discord.Embed(
                title=f"🔒 All {side} Positions Closed",
                color=0x00ff88 if total_pnl >= 0 else 0xff4444,
                timestamp=discord.utils.utcnow()
            )

            pnl_color = "🟢" if total_pnl >= 0 else "🔴"
            embed.add_field(name="Positions Closed", value=f"{closed_count}/{len(side_positions)}", inline=True)
            embed.add_field(name="Total P&L", value=f"{pnl_color} ${total_pnl:,.2f}", inline=True)

            embed.set_footer(text="Binance Futures Trading")
            await interaction.followup.send(embed=embed)

        except Exception as e:
            logger.error(f"Error in close side command: {e}")
            await interaction.followup.send(f"❌ Error: {str(e)}")

    @app_commands.command(name="closepos", description="Close specific position - /closepos symbol side percentage confirm:yes")
    @app_commands.describe(
        symbol="Symbol (vd: btc, eth)",
        side="Position side (LONG/SHORT)",
        percentage="Percentage to close (25/50/75/100)",
        confirm="Type 'yes' to confirm"
    )
    async def close_position_cmd(self, interaction: discord.Interaction,
                                symbol: str, side: str,
                                percentage: float, confirm: str):
        """Close specific position"""

        if not self._check_trade_channel(interaction):
            await interaction.response.send_message(
                f"❌ This command only works in #{self.trade_channel_name}",
                ephemeral=True
            )
            return

        if confirm != "yes":
            await interaction.response.send_message(
                f"⚠️ This will close {percentage}% of {symbol} {side} position! Add `confirm:yes` to proceed",
                ephemeral=True
            )
            return

        if not self.trading_service:
            await interaction.response.send_message("❌ Trading service not available", ephemeral=True)
            return

        await interaction.response.defer()

        try:
            symbol = self._normalize_symbol(symbol)
            side = side.upper()

            if side not in ['LONG', 'SHORT']:
                await interaction.followup.send("❌ Side must be LONG or SHORT")
                return

            if percentage not in [25.0, 50.0, 75.0, 100.0]:
                await interaction.followup.send("❌ Percentage must be 25, 50, 75, or 100")
                return

            result = await position_manager.close_position(symbol, side, percentage)

            if result.get('success'):
                embed = discord.Embed(
                    title="✅ Position Closed",
                    color=0x00ff88,
                    timestamp=discord.utils.utcnow()
                )
                embed.add_field(name="Symbol", value=symbol, inline=True)
                embed.add_field(name="Side", value=side, inline=True)
                embed.add_field(name="Percentage", value=f"{percentage}%", inline=True)
                embed.add_field(name="Order ID", value=result.get('order_id', 'N/A'), inline=False)
            else:
                embed = discord.Embed(
                    title="❌ Close Position Failed",
                    description=f"Error: {result.get('error')}",
                    color=0xff4444,
                    timestamp=discord.utils.utcnow()
                )

            embed.set_footer(text="Binance Futures Trading")
            await interaction.followup.send(embed=embed)

        except Exception as e:
            logger.error(f"Error in close position command: {e}")
            await interaction.followup.send(f"❌ Error: {str(e)}")

async def setup(bot):
    await bot.add_cog(PositionCommands(bot))
